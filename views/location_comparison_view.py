"""
Location Comparison View
========================

View component for comparing different project locations.
"""

import flet as ft
from typing import Dict, Any, Optional, List
import logging

from .base_view import BaseView
from components.charts.comparison_charts import ComparisonCharts
from components.ui.location_selection_widget import LocationSelectionWidget


class LocationComparisonView(BaseView):
    """View for location comparison and analysis."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.comparison_results: Optional[Dict[str, Any]] = None
        self.selected_locations: List[str] = ["Ouarzazate", "Dakhla"]
        self.comparison_charts = ComparisonCharts()
        self.logger = logging.getLogger(__name__)

        # Initialize location selection widget
        self.location_widget: Optional[LocationSelectionWidget] = None
    
    def build_content(self) -> ft.Control:
        """Build the location comparison view content."""
        
        # Header
        header = self.create_section_header(
            "Location Comparison",
            "Compare different locations for optimal project placement"
        )
        
        # Location selection
        location_selection = self._create_location_selection()
        
        # Comparison results
        if self.comparison_results:
            comparison_content = self._create_comparison_results()
        else:
            comparison_content = self.create_empty_state(
                "No Comparison Results",
                "Select locations and run comparison to see results",
                "Run Comparison",
                self._on_run_comparison
            )
        
        return ft.Column([
            header,
            location_selection,
            comparison_content
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_location_selection(self) -> ft.Card:
        """Create location selection interface using the reusable widget."""
        # Initialize location widget if not exists
        if not self.location_widget:
            self.location_widget = LocationSelectionWidget(self.page, compact_mode=False)
            self.location_widget.set_selected_locations(self.selected_locations)
            self.location_widget.set_selection_callback(self._on_location_selection_changed)
        
        # Create container with widget and action buttons
        selection_content = ft.Column([
            self.location_widget.build(),
            ft.Divider(height=20),
            ft.Row([
                self.create_action_button(
                    "Run Comparison",
                    ft.Icons.COMPARE_ARROWS,
                    self._on_run_comparison,
                    ft.Colors.GREEN_600
                )
            ], alignment=ft.MainAxisAlignment.CENTER)
        ])
        
        return self.create_card(
            "Location Selection & Comparison",
            selection_content,
            icon=ft.Icons.LOCATION_ON,
            bgcolor=ft.Colors.GREEN_50
        )
    
    def _create_comparison_results(self) -> ft.Column:
        """Create comparison results display."""
        if not self.comparison_results:
            return ft.Column()
        
        comparison_type = self.comparison_results.get('comparison_type', 'standard')
        
        if comparison_type == 'baseline_centric':
            return self._create_baseline_comparison_results()
        else:
            return self._create_standard_comparison_results()
    
    def _create_baseline_comparison_results(self) -> ft.Column:
        """Create baseline-centric comparison results display."""
        analysis = self.comparison_results.get('analysis', {})
        baseline_location = self.comparison_results.get('baseline_location', 'Unknown')
        
        # Baseline summary
        baseline_summary = self._create_baseline_summary(baseline_location, analysis)
        
        # Delta comparison table
        delta_table = self._create_delta_comparison_table(analysis)
        
        # Methodology explanation
        methodology = self._create_methodology_explanation()
        
        # Recommendations
        recommendations = self._create_baseline_recommendations(analysis)
        
        # Charts
        charts = self._create_comparison_charts()
        
        return ft.Column([
            methodology,
            baseline_summary,
            delta_table,
            recommendations,
            charts
        ])
    
    def _create_standard_comparison_results(self) -> ft.Column:
        """Create standard comparison results display (legacy)."""
        analysis = self.comparison_results.get('analysis', {})
        locations = self.comparison_results.get('locations', {})
        
        # Summary table
        summary_table = self._create_summary_table()
        
        # Rankings
        rankings = self._create_rankings()
        
        # Recommendations
        recommendations = self._create_recommendations()
        
        # Charts
        charts = self._create_comparison_charts()
        
        return ft.Column([
            summary_table,
            rankings,
            recommendations,
            charts
        ])
    
    def _create_summary_table(self) -> ft.Card:
        """Create comparison summary table."""
        analysis = self.comparison_results.get('analysis', {})
        comparison_matrix = analysis.get('comparison_matrix', [])
        
        if not comparison_matrix:
            return ft.Card()
        
        # Create table headers
        headers = [
            ft.DataColumn(ft.Text("Location")),
            ft.DataColumn(ft.Text("IRR Project")),
            ft.DataColumn(ft.Text("IRR Equity")),
            ft.DataColumn(ft.Text("NPV (M€)")),
            ft.DataColumn(ft.Text("LCOE (€/kWh)")),
            ft.DataColumn(ft.Text("Min DSCR")),
            ft.DataColumn(ft.Text("Cap. Factor"))
        ]
        
        # Create table rows
        rows = []
        for location_data in comparison_matrix:
            # Handle both legacy and new field naming conventions
            location_name = location_data.get('Location', location_data.get('location', 'Unknown'))
            irr_project = location_data.get('IRR_Project', location_data.get('irr_project', 0))
            irr_equity = location_data.get('IRR_Equity', location_data.get('irr_equity', 0))
            npv_meur = location_data.get('NPV_Project_MEUR', location_data.get('npv_project_meur', 0))
            lcoe = location_data.get('LCOE_EUR_kWh', location_data.get('lcoe_eur_kwh', 0))
            min_dscr = location_data.get('Min_DSCR', location_data.get('min_dscr', 0))
            capacity_factor = location_data.get('Capacity_Factor', location_data.get('capacity_factor', 0))
            
            # Special styling for baseline row
            is_baseline = location_data.get('type') == 'baseline'
            location_text = ft.Text(
                f"🏠 {location_name}" if is_baseline else location_name,
                weight=ft.FontWeight.BOLD if is_baseline else ft.FontWeight.NORMAL,
                color=ft.Colors.GREEN_700 if is_baseline else ft.Colors.BLACK
            )
            
            row = ft.DataRow(
                cells=[
                    ft.DataCell(location_text),
                    ft.DataCell(ft.Text(f"{irr_project:.1%}")),
                    ft.DataCell(ft.Text(f"{irr_equity:.1%}")),
                    ft.DataCell(ft.Text(f"{npv_meur:.1f}")),
                    ft.DataCell(ft.Text(f"{lcoe:.3f}")),
                    ft.DataCell(ft.Text(f"{min_dscr:.2f}")),
                    ft.DataCell(ft.Text(f"{capacity_factor:.1%}"))
                ],
                color=ft.Colors.GREEN_50 if is_baseline else None
            )
            rows.append(row)
        
        table = ft.DataTable(
            columns=headers,
            rows=rows,
            border=ft.border.all(1, ft.Colors.GREY_400),
            border_radius=8,
            vertical_lines=ft.border.BorderSide(1, ft.Colors.GREY_300),
            horizontal_lines=ft.border.BorderSide(1, ft.Colors.GREY_300)
        )
        
        return self.create_card(
            "Location Comparison Summary",
            ft.Container(content=table, padding=10),
            icon=ft.Icons.TABLE_CHART
        )
    
    def _create_rankings(self) -> ft.Card:
        """Create rankings display."""
        analysis = self.comparison_results.get('analysis', {})
        rankings = analysis.get('rankings', {})
        
        ranking_content = ft.Column([
            ft.Row([
                self._create_ranking_column("Best Project IRR", rankings.get('best_irr_project', [])),
                self._create_ranking_column("Best Equity IRR", rankings.get('best_irr_equity', [])),
                self._create_ranking_column("Lowest LCOE", rankings.get('lowest_lcoe', []))
            ])
        ])
        
        return self.create_card(
            "Location Rankings",
            ranking_content,
            icon=ft.Icons.LEADERBOARD
        )
    
    def _create_ranking_column(self, title: str, ranking_data: List[Dict]) -> ft.Container:
        """Create a ranking column."""
        ranking_items = []
        
        for item in ranking_data[:5]:  # Top 5
            rank_color = ft.Colors.AMBER if item['rank'] == 1 else ft.Colors.BLUE_GREY_400 if item['rank'] == 2 else ft.Colors.BROWN_700 if item['rank'] == 3 else ft.Colors.GREY
            
            ranking_items.append(
                ft.Row([
                    ft.Container(
                        content=ft.Text(str(item['rank']), color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                        width=30,
                        height=30,
                        bgcolor=rank_color,
                        border_radius=15,
                        alignment=ft.alignment.center
                    ),
                    ft.Text(item['location'], expand=True),
                    ft.Text(f"{item['value']:.3f}" if isinstance(item['value'], float) else str(item['value']))
                ])
            )
        
        return ft.Container(
            content=ft.Column([
                ft.Text(title, size=14, weight=ft.FontWeight.BOLD),
                ft.Divider(height=5),
                *ranking_items
            ]),
            padding=10,
            bgcolor=ft.Colors.GREY_50,
            border_radius=8,
            expand=True
        )
    
    def _create_recommendations(self) -> ft.Card:
        """Create recommendations display."""
        analysis = self.comparison_results.get('analysis', {})
        recommendations = analysis.get('recommendations', {})
        
        if not recommendations:
            return ft.Card()
        
        rec_content = ft.Column([
            ft.Text("Recommendations", size=18, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10)
        ])
        
        # Best overall location
        if 'best_overall' in recommendations:
            best_overall = recommendations['best_overall']
            rec_content.controls.append(
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.STAR, color=ft.Colors.AMBER),
                            ft.Text("Best Overall Location", size=16, weight=ft.FontWeight.BOLD)
                        ]),
                        ft.Text(f"Location: {best_overall['location']}", size=14),
                        ft.Text(f"Score: {best_overall['score']:.3f}", size=12, color=ft.Colors.GREY_600),
                        ft.Text("Strengths:", size=12, weight=ft.FontWeight.BOLD),
                        *[ft.Text(f"• {reason}", size=12) for reason in best_overall.get('reasons', [])]
                    ]),
                    padding=15,
                    bgcolor=ft.Colors.AMBER_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.AMBER)
                )
            )
        
        # Specific recommendations
        specific_recs = [
            ("Best for Returns", recommendations.get('best_for_returns')),
            ("Best for LCOE", recommendations.get('best_for_lcoe')),
            ("Best for Risk Management", recommendations.get('best_for_risk'))
        ]
        
        for title, location in specific_recs:
            if location:
                rec_content.controls.append(
                    self.create_info_row(title, location, ft.Colors.BLUE_700)
                )
        
        return self.create_card(
            "Analysis Recommendations",
            rec_content,
            icon=ft.Icons.RECOMMEND,
            bgcolor=ft.Colors.BLUE_50
        )
    
    def _create_methodology_explanation(self) -> ft.Card:
        """Create methodology explanation for baseline comparison."""
        methodology = self.comparison_results.get('comparison_methodology', {})
        constant_params = self.comparison_results.get('constant_parameters', [])
        variable_params = self.comparison_results.get('variable_parameters', [])
        
        content = ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.INFO, color=ft.Colors.BLUE_600),
                ft.Text("Baseline-Centric Comparison Methodology", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700)
            ]),
            ft.Divider(height=10),
            
            ft.Container(
                content=ft.Column([
                    ft.Text("📍 Baseline Approach", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700),
                    ft.Text(f"Current project location ({methodology.get('baseline', 'N/A')}) serves as the baseline for comparison.", size=12),
                    ft.Text("All financial parameters remain constant while technical parameters are adjusted by location.", size=12),
                ]),
                padding=10,
                bgcolor=ft.Colors.GREEN_50,
                border_radius=5,
                border=ft.border.all(1, ft.Colors.GREEN_200)
            ),
            
            ft.Container(height=10),
            
            ft.Row([
                ft.Column([
                    ft.Text("🔒 Constant Parameters", size=13, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                    ft.Column([
                        ft.Text(f"• {param}", size=11) for param in constant_params[:4]
                    ]) if constant_params else ft.Text("No constant parameters listed", size=11, italic=True)
                ], expand=True),
                
                ft.Column([
                    ft.Text("🔄 Variable Parameters", size=13, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_700),
                    ft.Column([
                        ft.Text(f"• {param}", size=11) for param in variable_params[:4]
                    ]) if variable_params else ft.Text("No variable parameters listed", size=11, italic=True)
                ], expand=True)
            ])
        ])
        
        return self.create_card(
            "Comparison Methodology",
            content,
            icon=ft.Icons.SCIENCE,
            bgcolor=ft.Colors.BLUE_50
        )
    
    def _create_baseline_summary(self, baseline_location: str, analysis: Dict[str, Any]) -> ft.Card:
        """Create baseline summary display."""
        baseline_analysis = analysis.get('baseline_analysis', {})
        
        if not baseline_analysis:
            return ft.Card()
        
        # Create KPI cards for baseline
        kpi_cards = ft.Row([
            self._create_kpi_card("IRR Project", f"{baseline_analysis.get('irr_project', 0):.1%}", ft.Colors.GREEN_600),
            self._create_kpi_card("IRR Equity", f"{baseline_analysis.get('irr_equity', 0):.1%}", ft.Colors.BLUE_600),
            self._create_kpi_card("NPV", f"€{baseline_analysis.get('npv_project_meur', 0):.1f}M", ft.Colors.PURPLE_600),
            self._create_kpi_card("LCOE", f"€{baseline_analysis.get('lcoe_eur_kwh', 0):.3f}/kWh", ft.Colors.ORANGE_600),
            self._create_kpi_card("Min DSCR", f"{baseline_analysis.get('min_dscr', 0):.2f}", ft.Colors.TEAL_600),
            self._create_kpi_card("Cap. Factor", f"{baseline_analysis.get('capacity_factor', 0):.1%}", ft.Colors.AMBER_600)
        ])
        
        content = ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.HOME, color=ft.Colors.GREEN_600),
                ft.Text(f"Baseline: {baseline_location}", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700)
            ]),
            ft.Text("Current project configuration serves as the baseline for comparison", size=12, color=ft.Colors.GREY_600),
            ft.Divider(height=15),
            kpi_cards
        ])
        
        return self.create_card(
            "Baseline Analysis",
            content,
            icon=ft.Icons.HOME,
            bgcolor=ft.Colors.GREEN_50
        )
    
    def _create_kpi_card(self, title: str, value: str, color: str) -> ft.Container:
        """Create a KPI card for display."""
        return ft.Container(
            content=ft.Column([
                ft.Text(title, size=12, weight=ft.FontWeight.BOLD, color=color, text_align=ft.TextAlign.CENTER),
                ft.Text(value, size=14, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            padding=10,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, color),
            width=120,
            height=70
        )
    
    def _create_delta_comparison_table(self, analysis: Dict[str, Any]) -> ft.Card:
        """Create delta comparison table showing differences from baseline."""
        comparison_matrix = analysis.get('comparison_matrix', [])
        delta_analysis = analysis.get('delta_analysis', {})
        
        if not comparison_matrix or not delta_analysis:
            return ft.Card()
        
        # Create table headers
        headers = [
            ft.DataColumn(ft.Text("Location", weight=ft.FontWeight.BOLD)),
            ft.DataColumn(ft.Text("IRR Δ", weight=ft.FontWeight.BOLD)),
            ft.DataColumn(ft.Text("NPV Δ (M€)", weight=ft.FontWeight.BOLD)),
            ft.DataColumn(ft.Text("LCOE Δ (€/kWh)", weight=ft.FontWeight.BOLD)),
            ft.DataColumn(ft.Text("Production Δ (MWh)", weight=ft.FontWeight.BOLD)),
            ft.DataColumn(ft.Text("Overall", weight=ft.FontWeight.BOLD))
        ]
        
        # Create table rows
        rows = []
        
        # Add baseline row first
        baseline_row = ft.DataRow(
            cells=[
                ft.DataCell(ft.Text("Baseline", weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700)),
                ft.DataCell(ft.Text("—", color=ft.Colors.GREY_600)),
                ft.DataCell(ft.Text("—", color=ft.Colors.GREY_600)),
                ft.DataCell(ft.Text("—", color=ft.Colors.GREY_600)),
                ft.DataCell(ft.Text("—", color=ft.Colors.GREY_600)),
                ft.DataCell(ft.Icon(ft.Icons.HOME, color=ft.Colors.GREEN_600, size=20))
            ],
            color=ft.Colors.GREEN_50
        )
        rows.append(baseline_row)
        
        # Add comparison rows
        for location, deltas in delta_analysis.items():
            irr_delta = deltas.get('irr_project_delta', 0)
            npv_delta = deltas.get('npv_delta_meur', 0)
            lcoe_delta = deltas.get('lcoe_delta_eur_kwh', 0)
            production_delta = deltas.get('production_delta_mwh', 0)
            
            # Determine overall assessment
            if irr_delta > 0.01 and npv_delta > 0:  # 1% IRR improvement and positive NPV
                overall_icon = ft.Icon(ft.Icons.TRENDING_UP, color=ft.Colors.GREEN_600, size=20)
                row_color = ft.Colors.GREEN_50
            elif irr_delta > 0 or npv_delta > 0:  # Some improvement
                overall_icon = ft.Icon(ft.Icons.TRENDING_FLAT, color=ft.Colors.ORANGE_600, size=20)
                row_color = ft.Colors.ORANGE_50
            else:  # Worse than baseline
                overall_icon = ft.Icon(ft.Icons.TRENDING_DOWN, color=ft.Colors.RED_600, size=20)
                row_color = ft.Colors.RED_50
            
            # Format deltas with colors
            irr_text = ft.Text(
                f"{irr_delta:+.1%}",
                color=ft.Colors.GREEN_600 if irr_delta > 0 else ft.Colors.RED_600 if irr_delta < 0 else ft.Colors.GREY_600
            )
            npv_text = ft.Text(
                f"{npv_delta:+.1f}",
                color=ft.Colors.GREEN_600 if npv_delta > 0 else ft.Colors.RED_600 if npv_delta < 0 else ft.Colors.GREY_600
            )
            lcoe_text = ft.Text(
                f"{lcoe_delta:+.3f}",
                color=ft.Colors.RED_600 if lcoe_delta > 0 else ft.Colors.GREEN_600 if lcoe_delta < 0 else ft.Colors.GREY_600
            )
            production_text = ft.Text(
                f"{production_delta:+.0f}",
                color=ft.Colors.GREEN_600 if production_delta > 0 else ft.Colors.RED_600 if production_delta < 0 else ft.Colors.GREY_600
            )
            
            row = ft.DataRow(
                cells=[
                    ft.DataCell(ft.Text(location)),
                    ft.DataCell(irr_text),
                    ft.DataCell(npv_text),
                    ft.DataCell(lcoe_text),
                    ft.DataCell(production_text),
                    ft.DataCell(overall_icon)
                ],
                color=row_color
            )
            rows.append(row)
        
        table = ft.DataTable(
            columns=headers,
            rows=rows,
            border=ft.border.all(1, ft.Colors.GREY_400),
            border_radius=8,
            vertical_lines=ft.border.BorderSide(1, ft.Colors.GREY_300),
            horizontal_lines=ft.border.BorderSide(1, ft.Colors.GREY_300)
        )
        
        # Add legend
        legend = ft.Row([
            ft.Row([
                ft.Icon(ft.Icons.TRENDING_UP, color=ft.Colors.GREEN_600, size=16),
                ft.Text("Better than baseline", size=10)
            ], spacing=5),
            ft.Row([
                ft.Icon(ft.Icons.TRENDING_FLAT, color=ft.Colors.ORANGE_600, size=16),
                ft.Text("Mixed results", size=10)
            ], spacing=5),
            ft.Row([
                ft.Icon(ft.Icons.TRENDING_DOWN, color=ft.Colors.RED_600, size=16),
                ft.Text("Worse than baseline", size=10)
            ], spacing=5)
        ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
        
        content = ft.Column([
            ft.Container(content=table, padding=10),
            ft.Divider(height=10),
            legend
        ])
        
        return self.create_card(
            "Baseline Comparison Results",
            content,
            icon=ft.Icons.COMPARE_ARROWS
        )
    
    def _create_baseline_recommendations(self, analysis: Dict[str, Any]) -> ft.Card:
        """Create recommendations for baseline comparison."""
        recommendations = analysis.get('recommendations', {})
        
        if not recommendations:
            return ft.Card()
        
        rec_content = ft.Column([
            ft.Text("Location Recommendations", size=18, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10)
        ])
        
        # Best overall alternative
        best_overall = recommendations.get('best_overall')
        if best_overall:
            rec_content.controls.extend([
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.STAR, color=ft.Colors.AMBER),
                            ft.Text("Best Alternative Location", size=16, weight=ft.FontWeight.BOLD)
                        ]),
                        ft.Text(f"Location: {best_overall['location']}", size=14),
                        ft.Text("Key Improvements:", size=12, weight=ft.FontWeight.BOLD),
                        *[ft.Text(f"• {reason}", size=12) for reason in best_overall.get('reasons', [])],
                        ft.Text("Location Advantages:", size=12, weight=ft.FontWeight.BOLD) if best_overall.get('location_advantages') else ft.Container(),
                        *[ft.Text(f"• {advantage}", size=12, color=ft.Colors.GREEN_700) 
                          for advantage in best_overall.get('location_advantages', [])]
                    ]),
                    padding=15,
                    bgcolor=ft.Colors.AMBER_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.AMBER)
                ),
                ft.Container(height=15)
            ])
        
        # Alternatives better than baseline
        better_alternatives = recommendations.get('alternatives_better_than_baseline', [])
        if better_alternatives:
            rec_content.controls.extend([
                ft.Text("Locations Better Than Baseline:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700),
                ft.Column([
                    ft.Text(f"• {alt['location']}: IRR +{alt['irr_improvement']:.1%}, NPV +€{alt['npv_improvement']:.1f}M", 
                           size=12, color=ft.Colors.GREEN_600)
                    for alt in better_alternatives
                ]),
                ft.Container(height=15)
            ])
        
        # Baseline advantages
        baseline_advantages = recommendations.get('baseline_advantages', [])
        if baseline_advantages:
            rec_content.controls.extend([
                ft.Text("Baseline Location Advantages:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                ft.Column([
                    ft.Text(f"• {advantage}", size=12, color=ft.Colors.BLUE_600)
                    for advantage in baseline_advantages
                ]),
                ft.Container(height=15)
            ])
        
        # Improvement opportunities
        opportunities = recommendations.get('improvement_opportunities', [])
        if opportunities:
            rec_content.controls.extend([
                ft.Text("Improvement Opportunities:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_700),
                ft.Column([
                    ft.Text(f"• {opportunity}", size=12, color=ft.Colors.PURPLE_600)
                    for opportunity in opportunities
                ])
            ])
        
        return self.create_card(
            "Analysis Recommendations",
            rec_content,
            icon=ft.Icons.LIGHTBULB,
            bgcolor=ft.Colors.BLUE_50
        )
    
    def _create_comparison_charts(self) -> ft.Card:
        """Create comparison charts."""
        if not self.comparison_results:
            return ft.Card()

        try:
            from components.charts.chart_factory import ChartFactory

            chart_factory = ChartFactory()
            charts_content = ft.Column([])

            # Get baseline analysis for industry benchmark
            analysis = self.comparison_results.get('analysis', {})
            baseline_analysis = analysis.get('baseline_analysis', {})

            if baseline_analysis:
                # Extract comprehensive project metrics for benchmark comparison
                project_metrics = {}
                
                # Financial metrics
                if baseline_analysis.get('irr_project', 0) > 0:
                    project_metrics['irr_project'] = baseline_analysis['irr_project']
                
                if baseline_analysis.get('irr_equity', 0) > 0:
                    project_metrics['irr_equity'] = baseline_analysis['irr_equity']
                
                if baseline_analysis.get('lcoe_eur_kwh', 0) > 0:
                    project_metrics['lcoe_eur_mwh'] = baseline_analysis['lcoe_eur_kwh'] * 1000
                
                if baseline_analysis.get('capacity_factor', 0) > 0:
                    project_metrics['capacity_factor'] = baseline_analysis['capacity_factor']
                
                # Extract CAPEX and OPEX if available
                if baseline_analysis.get('specific_capex', 0) > 0:
                    project_metrics['capex_eur_kw'] = baseline_analysis['specific_capex']
                elif baseline_analysis.get('capex_eur_kw', 0) > 0:
                    project_metrics['capex_eur_kw'] = baseline_analysis['capex_eur_kw']
                
                if baseline_analysis.get('opex_eur_kw_year', 0) > 0:
                    project_metrics['opex_eur_kw_year'] = baseline_analysis['opex_eur_kw_year']
                
                # DSCR if available
                if baseline_analysis.get('min_dscr', 0) > 0:
                    project_metrics['min_dscr'] = baseline_analysis['min_dscr']
                
                # NPV if available
                if baseline_analysis.get('npv_project', 0) != 0:
                    project_metrics['npv_project'] = baseline_analysis['npv_project'] / 1e6  # Convert to millions
                
                # Construction period with better default
                project_capacity = baseline_analysis.get('capacity_mw', 50)  # Default 50MW
                if project_capacity >= 100:
                    project_metrics['construction_period'] = 24  # Large projects: 24 months
                elif project_capacity >= 50:
                    project_metrics['construction_period'] = 18  # Medium projects: 18 months
                else:
                    project_metrics['construction_period'] = 12  # Small projects: 12 months

                if project_metrics:
                    # Get actual project technology and location from comparison results
                    baseline_location = self.comparison_results.get('baseline_location', 'Morocco')

                    # Determine technology from project data (default to solar if not specified)
                    technology = 'solar'  # Most projects are solar

                    # Map location to region for benchmarking (matching IndustryBenchmarksService regions)
                    location_to_region_map = {
                        # Africa
                        'Morocco': 'Africa',
                        'Ouarzazate': 'Africa',
                        'Dakhla': 'Africa',
                        'Casablanca': 'Africa',
                        'Rabat': 'Africa',
                        'Algeria': 'Africa',
                        'Egypt': 'Africa',
                        'Tunisia': 'Africa',
                        'South Africa': 'Africa',
                        # Europe
                        'Germany': 'Central Europe',
                        'Austria': 'Central Europe',
                        'Switzerland': 'Central Europe',
                        'Netherlands': 'Central Europe',
                        'Belgium': 'Central Europe',
                        'France': 'Central Europe',
                        'Spain': 'Southern Europe',
                        'Italy': 'Southern Europe',
                        'Portugal': 'Southern Europe',
                        'Greece': 'Southern Europe',
                        'Croatia': 'Southern Europe',
                        'Denmark': 'Northern Europe',
                        'Sweden': 'Northern Europe',
                        'Norway': 'Northern Europe',
                        'Finland': 'Northern Europe',
                        # Middle East
                        'UAE': 'Middle East',
                        'Saudi Arabia': 'Middle East',
                        'Jordan': 'Middle East',
                        'Israel': 'Middle East',
                        'Turkey': 'Middle East',
                        # Americas
                        'USA': 'North America',
                        'Canada': 'North America',
                        'Mexico': 'Latin America',
                        'Brazil': 'Latin America',
                        'Chile': 'Latin America',
                        # Asia Pacific
                        'India': 'Asia Pacific',
                        'China': 'Asia Pacific',
                        'Japan': 'Asia Pacific',
                        'Australia': 'Asia Pacific'
                    }

                    region = location_to_region_map.get(baseline_location, 'Central Europe')
                    
                    # Log for debugging
                    self.logger.info(f"Mapping location '{baseline_location}' to region '{region}' with {len(project_metrics)} metrics")

                    # Create industry benchmark chart with actual project data
                    benchmark_chart, _ = chart_factory.create_industry_benchmark_comparison(
                        project_metrics=project_metrics,
                        technology=technology,
                        region=region,
                        title=f"Project vs Industry Benchmarks ({baseline_location})"
                    )

                    charts_content.controls.append(
                        ft.Container(
                            content=benchmark_chart,
                            height=400,
                            padding=10
                        )
                    )

            # Add location comparison bar chart
            comparison_matrix = analysis.get('comparison_matrix', [])
            if comparison_matrix:
                location_chart = self._create_location_comparison_chart(comparison_matrix)
                charts_content.controls.append(
                    ft.Container(
                        content=location_chart,
                        height=300,
                        padding=10
                    )
                )

                # Add radar chart for multi-criteria comparison
                radar_data = self._prepare_radar_chart_data(comparison_matrix)
                if radar_data:
                    radar_chart, _ = chart_factory.create_location_comparison_radar(
                        location_data=radar_data,
                        title="Location Comparison - Multi-Criteria Analysis"
                    )
                    charts_content.controls.append(
                        ft.Container(
                            content=radar_chart,
                            height=400,
                            padding=10
                        )
                    )

            if not charts_content.controls:
                # Fallback placeholder
                chart_placeholder = ft.Container(
                    content=ft.Text("No chart data available",
                                  text_align=ft.TextAlign.CENTER),
                    height=300,
                    alignment=ft.alignment.center,
                    bgcolor=ft.Colors.GREY_100,
                    border_radius=8
                )
                charts_content.controls.append(chart_placeholder)

            return self.create_card(
                "Comparison Charts",
                charts_content,
                icon=ft.Icons.BAR_CHART
            )

        except Exception as e:
            self.logger.error(f"Error creating comparison charts: {e}")
            error_content = ft.Container(
                content=ft.Text(f"Error creating charts: {str(e)}",
                              text_align=ft.TextAlign.CENTER),
                height=300,
                alignment=ft.alignment.center,
                bgcolor=ft.Colors.RED_50,
                border_radius=8
            )

            return self.create_card(
                "Comparison Charts",
                error_content,
                icon=ft.Icons.ERROR
            )

    def _prepare_radar_chart_data(self, comparison_matrix: List[Dict[str, Any]]) -> Dict[str, Dict[str, float]]:
        """Prepare data for radar chart from comparison matrix."""
        radar_data = {}

        for location_data in comparison_matrix:
            location_name = location_data.get('Location', location_data.get('location', 'Unknown'))

            # Extract and normalize metrics for radar chart (0-100 scale)
            irr_project = location_data.get('IRR_Project', location_data.get('irr_project', 0)) * 100
            irr_equity = location_data.get('IRR_Equity', location_data.get('irr_equity', 0)) * 100
            npv_meur = location_data.get('NPV_Project_MEUR', location_data.get('npv_project_meur', 0))
            lcoe = location_data.get('LCOE_EUR_kWh', location_data.get('lcoe_eur_kwh', 0))
            min_dscr = location_data.get('Min_DSCR', location_data.get('min_dscr', 0))
            capacity_factor = location_data.get('Capacity_Factor', location_data.get('capacity_factor', 0)) * 100

            # Normalize metrics to 0-100 scale for radar chart
            radar_data[location_name] = {
                'IRR Project (%)': min(irr_project, 30),  # Cap at 30% for visualization
                'IRR Equity (%)': min(irr_equity, 35),    # Cap at 35% for visualization
                'NPV Score': min(max(npv_meur * 5, 0), 100),  # Scale NPV to 0-100
                'LCOE Score': min(max(100 - lcoe * 1000, 0), 100),  # Invert LCOE (lower is better)
                'DSCR Score': min(min_dscr * 50, 100),    # Scale DSCR to 0-100
                'Capacity Factor (%)': min(capacity_factor, 35)  # Cap at 35% for visualization
            }

        return radar_data

    def _create_location_comparison_chart(self, comparison_matrix: List[Dict]) -> ft.Container:
        """Create a simple location comparison bar chart."""
        try:
            import matplotlib.pyplot as plt
            import matplotlib
            matplotlib.use('Agg')
            import io
            import base64

            # Extract data for chart
            locations = []
            irr_values = []

            for location_data in comparison_matrix:
                location_name = location_data.get('Location', location_data.get('location', 'Unknown'))
                irr_project = location_data.get('IRR_Project', location_data.get('irr_project', 0))

                locations.append(location_name)
                irr_values.append(irr_project * 100)  # Convert to percentage

            if not locations:
                return ft.Container(
                    content=ft.Text("No location data available"),
                    height=200
                )

            # Create chart
            fig, ax = plt.subplots(figsize=(10, 6))
            bars = ax.bar(locations, irr_values, color=['#2E8B57', '#4169E1', '#FF8C00', '#DC143C'][:len(locations)])

            # Customize chart
            ax.set_title('Project IRR by Location', fontsize=14, fontweight='bold')
            ax.set_ylabel('IRR (%)', fontsize=12)
            ax.set_xlabel('Location', fontsize=12)
            ax.grid(True, alpha=0.3)

            # Add value labels on bars
            for bar, value in zip(bars, irr_values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')

            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()

            # Convert to image
            img_buffer = io.BytesIO()
            plt.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight')
            img_buffer.seek(0)
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
            plt.close(fig)

            # Create Flet image component
            return ft.Container(
                content=ft.Image(
                    src_base64=img_base64,
                    width=600,
                    height=300,
                    fit=ft.ImageFit.CONTAIN
                ),
                alignment=ft.alignment.center
            )

        except Exception as e:
            self.logger.error(f"Error creating location comparison chart: {e}")
            return ft.Container(
                content=ft.Text(f"Chart error: {str(e)}"),
                height=200
            )
    
    def _on_location_selection_changed(self, selected_locations: List[str]):
        """Handle location selection changes from the widget."""
        self.selected_locations = selected_locations
        self.refresh()
    
    def _on_run_comparison(self, e=None):
        """Run location comparison."""
        if len(self.selected_locations) < 2:
            self.show_error("Please select at least 2 locations for comparison")
            return
        
        self.request_action("run_location_comparison", {
            "locations": self.selected_locations
        })
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with comparison results."""
        if "comparison_results" in data:
            self.comparison_results = data["comparison_results"]
            self.refresh()
        
        if "selected_locations" in data:
            self.selected_locations = data["selected_locations"]
            self.refresh()
    
    def set_comparison_results(self, results: Dict[str, Any]):
        """Set comparison results."""
        self.comparison_results = results
        self.refresh()
    
    def get_selected_locations(self) -> List[str]:
        """Get currently selected locations."""
        return self.selected_locations.copy()
    
    def set_selected_locations(self, locations: List[str]):
        """Set selected locations."""
        self.selected_locations = locations
        if self.location_widget:
            self.location_widget.set_selected_locations(locations)
        self.refresh()
