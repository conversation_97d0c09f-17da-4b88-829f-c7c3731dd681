"""
Export Service
==============

Service for exporting data and reports in various formats.
"""

from typing import Dict, Any, Optional, Callable, List
import pandas as pd
import logging
from pathlib import Path
from datetime import datetime
import json
import base64
import io

from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from utils.file_utils import FileUtils

# Try to import DOCX dependencies at module level
try:
    from docx import Document
    from docx.shared import Inches, Pt, RGBColor
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.style import WD_STYLE_TYPE
    from docx.oxml.shared import OxmlElement, qn
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    # Create dummy constants to avoid NameError
    WD_ALIGN_PARAGRAPH = None
    Inches = None
    Pt = None
    Document = None
    RGBColor = None

# Try to import PDF dependencies
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    from reportlab.graphics.shapes import Drawing
    from reportlab.graphics.charts.barcharts import VerticalBarChart
    from reportlab.graphics.charts.linecharts import HorizontalLineChart
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

# Try to import PowerPoint dependencies
try:
    from pptx import Presentation
    from pptx.util import Inches as PPTXInches, Pt as PPTXPt
    from pptx.enum.text import PP_ALIGN
    from pptx.dml.color import RGBColor as PPTXRGBColor
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False


class ExportService:
    """Service for exporting data and generating reports."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.file_utils = FileUtils()
    
    def export_excel_report(self,
                          client_profile: ClientProfile,
                          assumptions: EnhancedProjectAssumptions,
                          financial_results: Dict[str, Any],
                          sensitivity_results: Optional[pd.DataFrame] = None,
                          monte_carlo_results: Optional[Dict[str, Any]] = None,
                          scenario_results: Optional[Dict[str, Any]] = None,
                          output_dir: Optional[Path] = None,
                          progress_callback: Optional[Callable[[float, str], None]] = None,
                          detailed_progress_callback: Optional[Callable[[str, str, float, str], None]] = None) -> Path:
        """Export comprehensive Excel report."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up Excel export...")
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "prepare_data", 0, "Setting up Excel export")
            
            # Create output directory
            if output_dir is None:
                output_dir = self.file_utils.create_timestamped_output_directory(client_profile)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"financial_model_{timestamp}.xlsx"
            filepath = output_dir['reports_dir'] / filename
            
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "prepare_data", 50, f"Created output directory: {output_dir['reports_dir']}")
            
            if progress_callback:
                progress_callback(30, "Preparing data for export...")
            
            # Prepare data for export
            scenarios_dict = scenario_results if scenario_results else {}
            sensitivity_df = sensitivity_results if sensitivity_results is not None else pd.DataFrame()
            mc_stats = monte_carlo_results.get('statistics', {}) if monte_carlo_results else {}
            
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "prepare_data", 100, "Data preparation completed")
            
            if progress_callback:
                progress_callback(60, "Writing enhanced Excel file with DCF analysis...")
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "generate_charts", 0, "Starting Excel generation")

            # Export using enhanced function with comprehensive DCF analysis
            self._export_enhanced_excel_with_dcf(
                financial_results=financial_results,
                client_profile=client_profile,
                assumptions=assumptions,
                scenarios_dict=scenarios_dict,
                sensitivity_df=sensitivity_df,
                mc_stats=mc_stats,
                filepath=filepath,
                detailed_progress_callback=detailed_progress_callback
            )
            
            if progress_callback:
                progress_callback(90, "Finalizing export...")
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "save_file", 0, "Adding metadata sheet")
            
            # Add metadata sheet
            self._add_metadata_sheet(filepath, client_profile, assumptions)
            
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "save_file", 100, "Excel file saved successfully")
            
            if progress_callback:
                progress_callback(100, "Excel export completed")
            
            self.logger.info(f"Excel report exported to: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting Excel report: {str(e)}")
            raise
    
    def export_docx_report(self,
                         client_profile: ClientProfile,
                         assumptions: EnhancedProjectAssumptions,
                         financial_results: Dict[str, Any],
                         validation_results: Optional[Any] = None,
                         charts: Optional[Dict[str, bytes]] = None,
                         output_dir: Optional[Path] = None,
                         progress_callback: Optional[Callable[[float, str], None]] = None,
                         detailed_progress_callback: Optional[Callable[[str, str, float, str], None]] = None) -> Path:
        """Export DOCX report with charts."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up DOCX export...")
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "prepare_data", 0, "Setting up DOCX export")
            
            # Check if DOCX functionality is available
            if not DOCX_AVAILABLE:
                raise ImportError("python-docx is required for DOCX export. Please install it with: pip install python-docx")
            
            # Create output directory
            if output_dir is None:
                output_dir = self.file_utils.create_timestamped_output_directory(client_profile)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"financial_report_{timestamp}.docx"
            filepath = output_dir['reports_dir'] / filename
            
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "prepare_data", 100, "DOCX setup completed")
            
            if progress_callback:
                progress_callback(30, "Creating DOCX document...")
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "create_document", 0, "Creating DOCX document")
            
            # Create document
            doc = Document()
            
            # Add title and client info
            self._add_docx_header(doc, client_profile, assumptions)
            
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "create_document", 40, "Added header and client info")
            
            if progress_callback:
                progress_callback(50, "Adding financial results...")
            
            # Add financial results
            self._add_docx_financial_results(doc, financial_results)
            
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "create_document", 70, "Added financial results")
            
            if progress_callback:
                progress_callback(70, "Adding charts...")
            
            # Add charts if provided
            if charts:
                self._add_docx_charts(doc, charts)
            
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "create_document", 90, "Added charts")
            
            if progress_callback:
                progress_callback(90, "Adding validation results...")
            
            # Add validation results
            if validation_results:
                self._add_docx_validation_results(doc, validation_results)
            
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "create_document", 100, "Document creation completed")
            
            # Save document
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "save_file", 0, "Saving DOCX file")
            
            doc.save(str(filepath))
            
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "save_file", 100, "DOCX file saved successfully")
            
            if progress_callback:
                progress_callback(100, "DOCX export completed")
            
            self.logger.info(f"DOCX report exported to: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting DOCX report: {str(e)}")
            raise
    
    def export_html_report(self,
                         client_profile: ClientProfile,
                         assumptions: EnhancedProjectAssumptions,
                         financial_results: Dict[str, Any],
                         charts: Optional[Dict[str, bytes]] = None,
                         output_dir: Optional[Path] = None,
                         progress_callback: Optional[Callable[[float, str], None]] = None) -> Path:
        """Export HTML report."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up HTML export...")
            
            # Create output directory
            if output_dir is None:
                output_dir = self.file_utils.create_timestamped_output_directory(client_profile)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"financial_report_{timestamp}.html"
            filepath = output_dir['reports_dir'] / filename
            
            if progress_callback:
                progress_callback(50, "Generating HTML content...")
            
            # Generate HTML content
            html_content = self._generate_html_report(client_profile, assumptions, financial_results, charts)
            
            if progress_callback:
                progress_callback(90, "Writing HTML file...")
            
            # Write HTML file
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            if progress_callback:
                progress_callback(100, "HTML export completed")
            
            self.logger.info(f"HTML report exported to: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting HTML report: {str(e)}")
            raise
    
    def export_json_data(self,
                        client_profile: ClientProfile,
                        assumptions: EnhancedProjectAssumptions,
                        financial_results: Dict[str, Any],
                        output_dir: Optional[Path] = None) -> Path:
        """Export data as JSON."""
        try:
            # Create output directory
            if output_dir is None:
                output_dir = self.file_utils.create_timestamped_output_directory(client_profile)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"financial_data_{timestamp}.json"
            filepath = output_dir['reports_dir'] / filename
            
            # Prepare data
            export_data = {
                'client_profile': client_profile.to_dict(),
                'assumptions': assumptions.to_dict(),
                'financial_results': self._serialize_financial_results(financial_results),
                'export_timestamp': datetime.now().isoformat(),
                'version': '2.0.0'
            }
            
            # Write JSON file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"JSON data exported to: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting JSON data: {str(e)}")
            raise

    def export_pdf_report(self,
                         client_profile: ClientProfile,
                         assumptions: EnhancedProjectAssumptions,
                         financial_results: Dict[str, Any],
                         charts: Optional[Dict[str, bytes]] = None,
                         output_dir: Optional[Path] = None,
                         progress_callback: Optional[Callable[[float, str], None]] = None) -> Path:
        """Export professional PDF report with branding."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up PDF export...")

            # Check if PDF functionality is available
            if not PDF_AVAILABLE:
                raise ImportError("reportlab is required for PDF export. Please install it with: pip install reportlab")

            # Create output directory
            if output_dir is None:
                output_dir = self.file_utils.create_timestamped_output_directory(client_profile)

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"financial_report_{timestamp}.pdf"
            filepath = output_dir['reports_dir'] / filename

            if progress_callback:
                progress_callback(30, "Creating PDF document...")

            # Create PDF document
            doc = SimpleDocTemplate(str(filepath), pagesize=A4)
            story = []

            # Get styles
            styles = getSampleStyleSheet()

            # Create custom styles
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#2E86AB')
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=16,
                spaceAfter=12,
                textColor=colors.HexColor('#2E86AB'),
                borderWidth=1,
                borderColor=colors.HexColor('#2E86AB'),
                borderPadding=5
            )

            if progress_callback:
                progress_callback(50, "Adding content to PDF...")

            # Add title
            story.append(Paragraph("Financial Model Report", title_style))
            story.append(Paragraph(f"<b>{client_profile.project_name}</b>", styles['Heading2']))
            story.append(Spacer(1, 20))

            # Add project information
            story.append(Paragraph("Project Information", heading_style))

            project_data = [
                ['Parameter', 'Value'],
                ['Client Company', client_profile.company_name],
                ['Project Name', client_profile.project_name],
                ['Capacity', f"{assumptions.capacity_mw} MW"],
                ['Location', client_profile.project_location or 'Not specified'],
                ['CAPEX', f"{assumptions.capex_meur} M EUR"],
                ['Report Date', client_profile.report_date]
            ]

            project_table = Table(project_data, colWidths=[3*inch, 3*inch])
            project_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(project_table)
            story.append(Spacer(1, 20))

            # Add financial results
            story.append(Paragraph("Key Performance Indicators", heading_style))

            kpis = financial_results.get('kpis', {})
            kpi_data = [
                ['Metric', 'Value'],
                ['Project IRR', f"{kpis.get('IRR_project', 0):.1%}"],
                ['Equity IRR', f"{kpis.get('IRR_equity', 0):.1%}"],
                ['NPV Project', f"{kpis.get('NPV_project', 0)/1e6:.2f} M EUR"],
                ['NPV Equity', f"{kpis.get('NPV_equity', 0)/1e6:.2f} M EUR"],
                ['LCOE', f"{kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh"],
                ['Min DSCR', f"{kpis.get('Min_DSCR', 0):.2f}"]
            ]

            kpi_table = Table(kpi_data, colWidths=[3*inch, 3*inch])
            kpi_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTNAME', (1, 1), (1, -1), 'Helvetica-Bold'),
                ('TEXTCOLOR', (1, 1), (1, -1), colors.HexColor('#2E8B57'))
            ]))

            story.append(kpi_table)
            story.append(Spacer(1, 20))

            if progress_callback:
                progress_callback(70, "Adding charts to PDF...")

            # Add charts if provided
            if charts:
                story.append(Paragraph("Financial Analysis Charts", heading_style))

                for chart_name, chart_bytes in charts.items():
                    chart_title = chart_name.replace('_', ' ').title()
                    story.append(Paragraph(chart_title, styles['Heading3']))

                    # Convert chart bytes to image
                    img_buffer = io.BytesIO(chart_bytes)
                    img = Image(img_buffer, width=6*inch, height=4*inch)
                    story.append(img)
                    story.append(Spacer(1, 12))

            if progress_callback:
                progress_callback(90, "Finalizing PDF...")

            # Add footer information
            story.append(Spacer(1, 30))
            story.append(Paragraph("Report Information", heading_style))

            footer_text = f"""
            <b>Consultant:</b> {client_profile.consultant}<br/>
            <b>Website:</b> {client_profile.consultant_website}<br/>
            <b>Tagline:</b> {client_profile.tagline}<br/>
            <b>Generated:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """

            story.append(Paragraph(footer_text, styles['Normal']))

            # Build PDF
            doc.build(story)

            if progress_callback:
                progress_callback(100, "PDF export completed")

            self.logger.info(f"PDF report exported to: {filepath}")
            return filepath

        except Exception as e:
            self.logger.error(f"Error exporting PDF report: {str(e)}")
            raise

    def export_interactive_dashboard(self,
                                   client_profile: ClientProfile,
                                   assumptions: EnhancedProjectAssumptions,
                                   analysis_results: Dict[str, Any],
                                   output_dir: Optional[Path] = None,
                                   progress_callback: Optional[Callable[[float, str], None]] = None) -> Path:
        """Export interactive HTML dashboard with Plotly charts."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up interactive dashboard...")

            # Import chart factory here to avoid circular imports
            from components.charts.chart_factory import ChartFactory
            chart_factory = ChartFactory()

            # Create output directory
            if output_dir is None:
                output_dir = self.file_utils.create_timestamped_output_directory(client_profile)

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"interactive_dashboard_{timestamp}.html"
            filepath = output_dir['reports_dir'] / filename

            if progress_callback:
                progress_callback(50, "Generating interactive dashboard...")

            # Generate interactive dashboard HTML
            dashboard_title = f"Interactive Financial Dashboard - {client_profile.project_name}"
            html_content = chart_factory.create_interactive_dashboard_html(
                analysis_results=analysis_results,
                title=dashboard_title
            )

            if progress_callback:
                progress_callback(90, "Writing dashboard file...")

            # Write HTML file
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)

            if progress_callback:
                progress_callback(100, "Interactive dashboard export completed")

            self.logger.info(f"Interactive dashboard exported to: {filepath}")
            return filepath

        except Exception as e:
            self.logger.error(f"Error exporting interactive dashboard: {str(e)}")
            raise

    def export_powerpoint_presentation(self,
                                     client_profile: ClientProfile,
                                     assumptions: EnhancedProjectAssumptions,
                                     financial_results: Dict[str, Any],
                                     charts: Optional[Dict[str, bytes]] = None,
                                     output_dir: Optional[Path] = None,
                                     progress_callback: Optional[Callable[[float, str], None]] = None) -> Path:
        """Export professional PowerPoint presentation."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up PowerPoint export...")

            # Check if PowerPoint functionality is available
            if not PPTX_AVAILABLE:
                raise ImportError("python-pptx is required for PowerPoint export. Please install it with: pip install python-pptx")

            # Create output directory
            if output_dir is None:
                output_dir = self.file_utils.create_timestamped_output_directory(client_profile)

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"financial_presentation_{timestamp}.pptx"
            filepath = output_dir['reports_dir'] / filename

            if progress_callback:
                progress_callback(30, "Creating PowerPoint presentation...")

            # Create presentation
            prs = Presentation()

            # Slide 1: Title slide
            title_slide_layout = prs.slide_layouts[0]
            slide = prs.slides.add_slide(title_slide_layout)
            title = slide.shapes.title
            subtitle = slide.placeholders[1]

            title.text = f"Financial Analysis\n{client_profile.project_name}"
            subtitle.text = f"""
{client_profile.company_name}
{assumptions.capacity_mw} MW {assumptions.technology_type} Project
{datetime.now().strftime('%B %Y')}

Prepared by: {client_profile.consultant}
"""

            if progress_callback:
                progress_callback(50, "Adding content slides...")

            # Slide 2: Executive Summary
            bullet_slide_layout = prs.slide_layouts[1]
            slide = prs.slides.add_slide(bullet_slide_layout)
            title = slide.shapes.title
            content = slide.placeholders[1]

            title.text = "Executive Summary"

            kpis = financial_results.get('kpis', {})
            content.text = f"""Key Financial Metrics:
• Project IRR: {kpis.get('IRR_project', 0):.1%}
• Equity IRR: {kpis.get('IRR_equity', 0):.1%}
• NPV Project: €{kpis.get('NPV_project', 0)/1e6:.1f}M
• LCOE: {kpis.get('LCOE_eur_kwh', 0):.3f} €/kWh
• Min DSCR: {kpis.get('Min_DSCR', 0):.2f}

Investment Recommendation:
• {'Strong fundamentals with attractive returns' if kpis.get('IRR_project', 0) > 0.12 else 'Moderate returns requiring optimization' if kpis.get('IRR_project', 0) > 0.08 else 'Requires significant restructuring'}
• {'Robust debt coverage' if kpis.get('Min_DSCR', 0) > 1.25 else 'Adequate debt coverage' if kpis.get('Min_DSCR', 0) > 1.0 else 'Weak debt coverage'}
• {'Competitive cost structure' if kpis.get('LCOE_eur_kwh', 0) < 0.045 else 'Moderate cost competitiveness'}"""

            # Slide 3: Project Overview
            slide = prs.slides.add_slide(bullet_slide_layout)
            title = slide.shapes.title
            content = slide.placeholders[1]

            title.text = "Project Overview"
            content.text = f"""Project Specifications:
• Technology: {assumptions.technology_type}
• Capacity: {assumptions.capacity_mw} MW
• Location: {client_profile.project_location or 'TBD'}
• Project Life: {assumptions.project_life_years} years

Investment Structure:
• Total CAPEX: €{assumptions.capex_meur:.1f}M
• Equity: €{assumptions.equity_meur:.1f}M ({assumptions.equity_percentage:.1%})
• Debt: €{assumptions.debt_meur:.1f}M ({(1-assumptions.equity_percentage):.1%})
• Grant Support: €{assumptions.calculate_total_grants():.1f}M"""

            if progress_callback:
                progress_callback(70, "Adding charts to presentation...")

            # Add chart slides if charts are provided
            if charts:
                chart_slide_layout = prs.slide_layouts[5]  # Blank layout

                for i, (chart_name, chart_bytes) in enumerate(charts.items()):
                    if i >= 5:  # Limit to 5 charts to keep presentation concise
                        break

                    slide = prs.slides.add_slide(chart_slide_layout)

                    # Add title
                    title_shape = slide.shapes.add_textbox(
                        PPTXInches(0.5), PPTXInches(0.5),
                        PPTXInches(9), PPTXInches(1)
                    )
                    title_frame = title_shape.text_frame
                    title_frame.text = chart_name.replace('_', ' ').title()
                    title_frame.paragraphs[0].font.size = PPTXPt(24)
                    title_frame.paragraphs[0].font.bold = True

                    # Add chart image
                    img_stream = io.BytesIO(chart_bytes)
                    slide.shapes.add_picture(
                        img_stream,
                        PPTXInches(1), PPTXInches(1.5),
                        PPTXInches(8), PPTXInches(5.5)
                    )

            # Final slide: Next Steps
            slide = prs.slides.add_slide(bullet_slide_layout)
            title = slide.shapes.title
            content = slide.placeholders[1]

            title.text = "Next Steps & Recommendations"

            if kpis.get('IRR_project', 0) > 0.12:
                next_steps = """Immediate Actions:
• Proceed with detailed due diligence
• Initiate financing discussions
• Begin EPC contractor selection
• Establish project timeline

Key Success Factors:
• Maintain cost discipline
• Secure favorable financing terms
• Execute construction on schedule
• Optimize operational performance"""
            else:
                next_steps = """Optimization Required:
• Review project economics
• Explore cost reduction opportunities
• Consider alternative financing
• Assess technology alternatives

Risk Mitigation:
• Detailed sensitivity analysis
• Contingency planning
• Insurance coverage review
• Stakeholder engagement"""

            content.text = next_steps

            if progress_callback:
                progress_callback(90, "Finalizing presentation...")

            # Save presentation
            prs.save(str(filepath))

            if progress_callback:
                progress_callback(100, "PowerPoint export completed")

            self.logger.info(f"PowerPoint presentation exported to: {filepath}")
            return filepath

        except Exception as e:
            self.logger.error(f"Error exporting PowerPoint presentation: {str(e)}")
            raise
    
    def _add_metadata_sheet(self, filepath: Path, client_profile: ClientProfile, assumptions: EnhancedProjectAssumptions):
        """Add metadata sheet to Excel file."""
        try:
            # Read existing Excel file
            with pd.ExcelWriter(str(filepath), mode='a', engine='openpyxl') as writer:
                # Create metadata DataFrame
                metadata = {
                    'Parameter': [
                        'Client Company', 'Client Name', 'Project Name', 'Report Date',
                        'Consultant', 'Project Capacity (MW)', 'Project Life (years)',
                        'CAPEX (M EUR)', 'Total Grants (M EUR)', 'Grant Percentage (%)',
                        'Export Timestamp'
                    ],
                    'Value': [
                        client_profile.company_name,
                        client_profile.client_name,
                        client_profile.project_name,
                        client_profile.report_date,
                        client_profile.consultant,
                        assumptions.capacity_mw,
                        assumptions.project_life_years,
                        assumptions.capex_meur,
                        assumptions.calculate_total_grants(),
                        assumptions.calculate_grant_percentage(),
                        datetime.now().isoformat()
                    ]
                }
                
                metadata_df = pd.DataFrame(metadata)
                metadata_df.to_excel(writer, sheet_name='Metadata', index=False)
                
        except Exception as e:
            self.logger.warning(f"Could not add metadata sheet: {str(e)}")
    
    def _add_docx_header(self, doc, client_profile: ClientProfile, assumptions: EnhancedProjectAssumptions):
        """Add header to DOCX document."""
        # Title
        title = doc.add_heading('Financial Model Report', 0)
        if WD_ALIGN_PARAGRAPH is not None:
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Client information
        doc.add_heading('Project Information', level=1)
        
        info_table = doc.add_table(rows=6, cols=2)
        info_table.style = 'Table Grid'
        
        info_data = [
            ('Client Company', client_profile.company_name),
            ('Project Name', client_profile.project_name),
            ('Capacity', f"{assumptions.capacity_mw} MW"),
            ('Location', client_profile.project_location or 'Not specified'),
            ('Report Date', client_profile.report_date),
            ('Consultant', client_profile.consultant)
        ]
        
        for i, (label, value) in enumerate(info_data):
            info_table.cell(i, 0).text = label
            info_table.cell(i, 1).text = str(value)
    
    def _add_docx_financial_results(self, doc, financial_results: Dict[str, Any]):
        """Add financial results to DOCX document."""
        doc.add_heading('Financial Results', level=1)
        
        kpis = financial_results.get('kpis', {})
        
        # KPI table
        kpi_table = doc.add_table(rows=7, cols=2)
        kpi_table.style = 'Table Grid'
        
        kpi_data = [
            ('Project IRR', f"{kpis.get('IRR_project', 0):.1%}"),
            ('Equity IRR', f"{kpis.get('IRR_equity', 0):.1%}"),
            ('NPV Project (M EUR)', f"{kpis.get('NPV_project', 0)/1e6:.2f}"),
            ('NPV Equity (M EUR)', f"{kpis.get('NPV_equity', 0)/1e6:.2f}"),
            ('LCOE (EUR/kWh)', f"{kpis.get('LCOE_eur_kwh', 0):.3f}"),
            ('Min DSCR', f"{kpis.get('Min_DSCR', 0):.2f}"),
            ('Payback Period (years)', f"{kpis.get('Payback_years', 0):.1f}")
        ]
        
        for i, (label, value) in enumerate(kpi_data):
            kpi_table.cell(i, 0).text = label
            kpi_table.cell(i, 1).text = value
    
    def _add_docx_charts(self, doc, charts: Dict[str, bytes]):
        """Add charts to DOCX document."""
        doc.add_heading('Charts and Analysis', level=1)
        
        for chart_name, chart_data in charts.items():
            doc.add_heading(chart_name.replace('_', ' ').title(), level=2)
            
            # Save chart as temporary file and add to document
            temp_path = Path(f"temp_{chart_name}.png")
            try:
                with open(temp_path, 'wb') as f:
                    f.write(chart_data)
                if Inches is not None:
                    doc.add_picture(str(temp_path), width=Inches(6))
                else:
                    doc.add_picture(str(temp_path))
                temp_path.unlink()  # Delete temporary file
            except Exception as e:
                doc.add_paragraph(f"Chart could not be embedded: {str(e)}")
    
    def _add_docx_validation_results(self, doc, validation_results):
        """Add validation results to DOCX document."""
        doc.add_heading('Model Validation', level=1)
        
        # Validation status
        status = "PASSED" if validation_results.is_valid else "FAILED"
        doc.add_paragraph(f"Validation Status: {status}")
        
        # Warnings
        if validation_results.warnings:
            doc.add_heading('Warnings', level=2)
            for warning in validation_results.warnings:
                doc.add_paragraph(f"• {warning}")
        
        # Errors
        if validation_results.errors:
            doc.add_heading('Errors', level=2)
            for error in validation_results.errors:
                doc.add_paragraph(f"• {error}")
    
    def _generate_html_report(self, client_profile: ClientProfile, assumptions: EnhancedProjectAssumptions, financial_results: Dict[str, Any], charts: Optional[Dict[str, bytes]] = None) -> str:
        """Generate HTML report content."""
        kpis = financial_results.get('kpis', {})
        
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Financial Model Report - {client_profile.project_name}</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                /* Professional 2025 Styling */
                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                }}

                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                    background: white;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                    border-radius: 10px;
                    margin-top: 20px;
                    margin-bottom: 20px;
                }}

                .header {{
                    text-align: center;
                    margin-bottom: 40px;
                    padding: 30px 0;
                    background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
                    color: white;
                    border-radius: 10px;
                    box-shadow: 0 4px 15px rgba(46, 134, 171, 0.3);
                }}

                .header h1 {{
                    font-size: 2.5em;
                    margin-bottom: 10px;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                }}

                .header h2 {{
                    font-size: 1.5em;
                    margin-bottom: 10px;
                    opacity: 0.9;
                }}

                .section {{
                    margin-bottom: 40px;
                    padding: 25px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    border-left: 5px solid #2E86AB;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                }}

                .section h3 {{
                    color: #2E86AB;
                    font-size: 1.4em;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 2px solid #2E86AB;
                }}

                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin-top: 15px;
                    background: white;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}

                th {{
                    background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
                    color: white;
                    padding: 15px;
                    text-align: left;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }}

                td {{
                    padding: 12px 15px;
                    border-bottom: 1px solid #eee;
                    transition: background-color 0.3s ease;
                }}

                tr:hover td {{
                    background-color: #f5f5f5;
                }}

                .kpi-value {{
                    font-weight: bold;
                    color: #2E8B57;
                    font-size: 1.1em;
                }}

                .chart-container {{
                    margin: 25px 0;
                    padding: 20px;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    text-align: center;
                }}

                .chart-container h4 {{
                    color: #2E86AB;
                    margin-bottom: 15px;
                    font-size: 1.2em;
                }}

                .chart-container img {{
                    max-width: 100%;
                    height: auto;
                    border-radius: 5px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }}

                .footer {{
                    text-align: center;
                    margin-top: 40px;
                    padding: 20px;
                    background: #2E86AB;
                    color: white;
                    border-radius: 8px;
                }}

                .badge {{
                    display: inline-block;
                    padding: 5px 10px;
                    background: #2E8B57;
                    color: white;
                    border-radius: 15px;
                    font-size: 0.9em;
                    margin-left: 10px;
                }}

                @media print {{
                    body {{ background: white; }}
                    .container {{ box-shadow: none; }}
                }}

                @media (max-width: 768px) {{
                    .container {{ margin: 10px; padding: 15px; }}
                    .header h1 {{ font-size: 2em; }}
                    table {{ font-size: 0.9em; }}
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Financial Model Report</h1>
                    <h2>{client_profile.project_name}</h2>
                    <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <span class="badge">Professional Analysis</span>
                </div>

                <div class="section">
                    <h3>📊 Project Information</h3>
                    <table>
                        <tr><th>Parameter</th><th>Value</th></tr>
                        <tr><td>Client Company</td><td><strong>{client_profile.company_name}</strong></td></tr>
                        <tr><td>Project Name</td><td><strong>{client_profile.project_name}</strong></td></tr>
                        <tr><td>Capacity</td><td>{assumptions.capacity_mw} MW</td></tr>
                        <tr><td>Location</td><td>{client_profile.project_location or 'Not specified'}</td></tr>
                        <tr><td>CAPEX</td><td>{assumptions.capex_meur} M EUR</td></tr>
                        <tr><td>Project Life</td><td>{assumptions.project_life_years} years</td></tr>
                        <tr><td>Technology</td><td>{assumptions.technology_type}</td></tr>
                    </table>
                </div>

                <div class="section">
                    <h3>💰 Key Performance Indicators</h3>
                    <table>
                        <tr><th>Financial Metric</th><th>Value</th><th>Status</th></tr>
                        <tr>
                            <td>Project IRR</td>
                            <td class="kpi-value">{kpis.get('IRR_project', 0):.1%}</td>
                            <td>{'✅ Excellent' if kpis.get('IRR_project', 0) > 0.12 else '⚠️ Review' if kpis.get('IRR_project', 0) > 0.08 else '❌ Poor'}</td>
                        </tr>
                        <tr>
                            <td>Equity IRR</td>
                            <td class="kpi-value">{kpis.get('IRR_equity', 0):.1%}</td>
                            <td>{'✅ Excellent' if kpis.get('IRR_equity', 0) > 0.15 else '⚠️ Review' if kpis.get('IRR_equity', 0) > 0.10 else '❌ Poor'}</td>
                        </tr>
                        <tr>
                            <td>NPV Project</td>
                            <td class="kpi-value">{kpis.get('NPV_project', 0)/1e6:.2f} M EUR</td>
                            <td>{'✅ Positive' if kpis.get('NPV_project', 0) > 0 else '❌ Negative'}</td>
                        </tr>
                        <tr>
                            <td>NPV Equity</td>
                            <td class="kpi-value">{kpis.get('NPV_equity', 0)/1e6:.2f} M EUR</td>
                            <td>{'✅ Positive' if kpis.get('NPV_equity', 0) > 0 else '❌ Negative'}</td>
                        </tr>
                        <tr>
                            <td>LCOE</td>
                            <td class="kpi-value">{kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh</td>
                            <td>{'✅ Competitive' if kpis.get('LCOE_eur_kwh', 0) < 0.045 else '⚠️ Moderate' if kpis.get('LCOE_eur_kwh', 0) < 0.060 else '❌ High'}</td>
                        </tr>
                        <tr>
                            <td>Min DSCR</td>
                            <td class="kpi-value">{kpis.get('Min_DSCR', 0):.2f}</td>
                            <td>{'✅ Strong' if kpis.get('Min_DSCR', 0) > 1.25 else '⚠️ Adequate' if kpis.get('Min_DSCR', 0) > 1.0 else '❌ Weak'}</td>
                        </tr>
                    </table>
                </div>"""

        # Add charts section if charts are provided
        if charts:
            html_template += """
                <div class="section">
                    <h3>📈 Financial Analysis Charts</h3>
                    <p style="margin-bottom: 20px; color: #666;">Comprehensive visual analysis of project financials, risks, and market positioning.</p>"""

            for chart_name, chart_bytes in charts.items():
                # Convert chart bytes to base64 for embedding
                chart_base64 = base64.b64encode(chart_bytes).decode('utf-8')
                chart_title = chart_name.replace('_', ' ').title()

                html_template += f"""
                    <div class="chart-container">
                        <h4>{chart_title}</h4>
                        <img src="data:image/png;base64,{chart_base64}" alt="{chart_title}">
                    </div>"""

            html_template += """
                </div>"""

        html_template += f"""
                <div class="section">
                    <h3>🏢 Report Information</h3>
                    <table>
                        <tr><th>Parameter</th><th>Value</th></tr>
                        <tr><td>Consultant</td><td><strong>{client_profile.consultant}</strong></td></tr>
                        <tr><td>Website</td><td><a href="{client_profile.consultant_website}" target="_blank">{client_profile.consultant_website}</a></td></tr>
                        <tr><td>Tagline</td><td><em>{client_profile.tagline}</em></td></tr>
                        <tr><td>Report Version</td><td>Professional 2025 Edition</td></tr>
                        <tr><td>Analysis Depth</td><td>Comprehensive Multi-Scenario</td></tr>
                    </table>
                </div>

                <div class="footer">
                    <p><strong>Professional Financial Analysis Report</strong></p>
                    <p>Generated with advanced modeling techniques and industry best practices</p>
                    <p style="margin-top: 10px; opacity: 0.8;">© 2025 {client_profile.consultant} - All Rights Reserved</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_template
    
    def _serialize_financial_results(self, financial_results: Dict[str, Any]) -> Dict[str, Any]:
        """Serialize financial results for JSON export."""
        serialized = {}
        
        for key, value in financial_results.items():
            if isinstance(value, pd.DataFrame):
                serialized[key] = value.to_dict('records')
            elif isinstance(value, (pd.Series, pd.Index)):
                serialized[key] = value.to_list()
            else:
                serialized[key] = value
        
        return serialized

    def _export_enhanced_excel_with_dcf(self,
                                       financial_results: Dict[str, Any],
                                       client_profile: ClientProfile,
                                       assumptions: EnhancedProjectAssumptions,
                                       scenarios_dict: Dict[str, Any],
                                       sensitivity_df: pd.DataFrame,
                                       mc_stats: Dict[str, Dict[str, float]],
                                       filepath: Path,
                                       detailed_progress_callback: Optional[Callable[[str, str, float, str], None]] = None):
        """Export comprehensive Excel report with enhanced DCF analysis - 2025 Edition."""

        with pd.ExcelWriter(str(filepath), engine='xlsxwriter') as writer:
            workbook = writer.book

            # Enhanced 2025 formatting
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#2E86AB',
                'font_color': 'white',
                'border': 1,
                'font_size': 12
            })

            currency_millions_format = workbook.add_format({
                'num_format': '€#,##0.0,,"M"',
                'border': 1
            })

            percentage_format = workbook.add_format({
                'num_format': '0.00%',
                'border': 1
            })

            # 1. Executive Summary
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "generate_charts", 10, "Creating executive summary")
            self._write_executive_summary_dcf(writer, financial_results, client_profile, assumptions, workbook)

            # 2. Detailed DCF Cash Flow
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "generate_charts", 20, "Creating DCF cashflow sheet")
            self._write_dcf_cashflow_detailed(writer, financial_results, workbook)

            # 3. KPI Analysis with DCF metrics
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "generate_charts", 30, "Creating KPI analysis sheet")
            self._write_kpi_analysis_dcf(writer, financial_results, workbook)

            # 4. NPV Breakdown Analysis
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "generate_charts", 40, "Creating NPV breakdown sheet")
            self._write_npv_breakdown(writer, financial_results, workbook)

            # 5. Sensitivity Analysis with Interpretation
            if not sensitivity_df.empty:
                if detailed_progress_callback:
                    detailed_progress_callback("data_export", "generate_charts", 50, "Creating sensitivity analysis sheet")
                self._write_sensitivity_analysis_detailed(writer, sensitivity_df, financial_results, workbook)

            # 6. Scenario Comparison Analysis
            if scenarios_dict:
                if detailed_progress_callback:
                    detailed_progress_callback("data_export", "generate_charts", 60, "Creating scenario comparison sheet")
                self._write_scenario_comparison(writer, scenarios_dict, workbook)

            # 7. Monte Carlo Analysis Summary
            if mc_stats:
                if detailed_progress_callback:
                    detailed_progress_callback("data_export", "generate_charts", 70, "Creating Monte Carlo summary sheet")
                self._write_monte_carlo_summary(writer, mc_stats, workbook)

            # 8. Model Validation & 2025 Standards
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "generate_charts", 80, "Creating model validation sheet")
            self._write_model_validation_2025(writer, financial_results, assumptions, workbook)

            # 9. Risk Analysis & Recommendations
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "generate_charts", 90, "Creating risk analysis sheet")
            self._write_risk_analysis(writer, financial_results, assumptions, workbook)

            # 10. Charts Reference Sheet
            if detailed_progress_callback:
                detailed_progress_callback("data_export", "generate_charts", 100, "Creating charts reference sheet")
            self._write_charts_reference(writer, workbook)

    def _write_executive_summary_dcf(self, writer, financial_results, client_profile, assumptions, workbook):
        """Write executive summary with DCF highlights."""
        kpis = financial_results.get('kpis', {})

        summary_data = [
            ['PROJECT OVERVIEW', ''],
            ['Client Company', client_profile.company_name],
            ['Project Name', client_profile.project_name],
            ['Capacity (MW)', assumptions.capacity_mw],
            ['CAPEX (M EUR)', assumptions.capex_meur],
            ['Total Grants (M EUR)', assumptions.calculate_total_grants()],
            ['', ''],
            ['DCF ANALYSIS RESULTS - 2025', ''],
            ['Project IRR', f"{kpis.get('IRR_project', 0):.2%}"],
            ['Equity IRR', f"{kpis.get('IRR_equity', 0):.2%}"],
            ['NPV Project (M EUR)', f"{kpis.get('NPV_project', 0)/1e6:.2f}"],
            ['NPV Equity (M EUR)', f"{kpis.get('NPV_equity', 0)/1e6:.2f}"],
            ['LCOE (EUR/kWh)', f"{kpis.get('LCOE_eur_kwh', 0):.4f}"],
            ['Terminal Value (M EUR)', f"{kpis.get('Terminal_value', 0)/1e6:.2f}"],
            ['Min DSCR', f"{kpis.get('Min_DSCR', 0):.2f}"],
            ['Payback Period (years)', f"{kpis.get('Payback_years', 0):.1f}"],
            ['', ''],
            ['MODEL INFORMATION', ''],
            ['Model Version', financial_results.get('model_version', '3.0.0_DCF_Enhanced')],
            ['Execution Time', financial_results.get('execution_time', '')],
            ['Report Generated', datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
        ]

        summary_df = pd.DataFrame(summary_data, columns=['Parameter', 'Value'])
        summary_df.to_excel(writer, sheet_name='Executive_Summary', index=False)

    def _write_dcf_cashflow_detailed(self, writer, financial_results, workbook):
        """Write detailed DCF cashflow analysis."""
        cashflow = financial_results.get('cashflow')
        if cashflow is not None:
            if isinstance(cashflow, dict):
                cashflow_df = pd.DataFrame(cashflow)
            else:
                cashflow_df = cashflow.copy()

            # Ensure we have a proper year column
            if 'Year' not in cashflow_df.columns:
                if cashflow_df.index.name == 'Year' or all(isinstance(idx, (int, float)) for idx in cashflow_df.index):
                    cashflow_df.reset_index(inplace=True)
                    if 'index' in cashflow_df.columns:
                        cashflow_df.rename(columns={'index': 'Year'}, inplace=True)
                else:
                    cashflow_df.insert(0, 'Year', range(len(cashflow_df)))

            # Add DCF-specific calculations
            if 'Free_Cash_Flow_Project' in cashflow_df.columns:
                discount_rate = financial_results.get('dcf_assumptions', {}).get('discount_rate', 0.08)

                # Calculate discount factors and present values
                cashflow_df['Discount_Factor'] = [1 / (1 + discount_rate) ** year if year > 0 else 1
                                                 for year in cashflow_df['Year']]
                cashflow_df['PV_Project_CF'] = cashflow_df['Free_Cash_Flow_Project'] * cashflow_df['Discount_Factor']

                if 'Free_Cash_Flow_Equity' in cashflow_df.columns:
                    cashflow_df['PV_Equity_CF'] = cashflow_df['Free_Cash_Flow_Equity'] * cashflow_df['Discount_Factor']

            # Export to Excel
            cashflow_df.to_excel(writer, sheet_name='DCF_Cashflow_Detailed', index=False)

            # Format the sheet with enhanced formatting
            worksheet = writer.sheets['DCF_Cashflow_Detailed']

            # Set column widths
            worksheet.set_column('A:A', 8)   # Year column
            worksheet.set_column('B:Z', 15)  # Other columns

            # Add formatting for financial data
            currency_format = workbook.add_format({'num_format': '€#,##0', 'border': 1})
            percentage_format = workbook.add_format({'num_format': '0.00%', 'border': 1})
            header_format = workbook.add_format({
                'bold': True, 'bg_color': '#2E86AB', 'font_color': 'white', 'border': 1
            })

            # Apply header formatting
            for col_num, value in enumerate(cashflow_df.columns):
                worksheet.write(0, col_num, value, header_format)

            # Apply currency formatting to cash flow columns
            cash_flow_cols = ['Free_Cash_Flow_Project', 'Free_Cash_Flow_Equity', 'PV_Project_CF', 'PV_Equity_CF',
                             'Revenue', 'EBITDA', 'CAPEX', 'Debt_Service']
            for col_name in cash_flow_cols:
                if col_name in cashflow_df.columns:
                    col_idx = cashflow_df.columns.get_loc(col_name)
                    worksheet.set_column(col_idx, col_idx, 15, currency_format)

    def _write_kpi_analysis_dcf(self, writer, financial_results, workbook):
        """Write comprehensive KPI analysis with DCF metrics."""
        kpis = financial_results.get('kpis', {})

        kpi_analysis = [
            ['FINANCIAL RETURNS', ''],
            ['Project IRR', f"{kpis.get('IRR_project', 0):.4f}", f"{kpis.get('IRR_project', 0):.2%}"],
            ['Equity IRR', f"{kpis.get('IRR_equity', 0):.4f}", f"{kpis.get('IRR_equity', 0):.2%}"],
            ['', '', ''],
            ['NET PRESENT VALUE ANALYSIS', '', ''],
            ['NPV Project (EUR)', f"{kpis.get('NPV_project', 0):,.0f}", f"{kpis.get('NPV_project', 0)/1e6:.2f} M EUR"],
            ['NPV Equity (EUR)', f"{kpis.get('NPV_equity', 0):,.0f}", f"{kpis.get('NPV_equity', 0)/1e6:.2f} M EUR"],
            ['Terminal Value (EUR)', f"{kpis.get('Terminal_value', 0):,.0f}", f"{kpis.get('Terminal_value', 0)/1e6:.2f} M EUR"],
            ['', '', ''],
            ['OPERATIONAL METRICS', '', ''],
            ['LCOE (EUR/kWh)', f"{kpis.get('LCOE_eur_kwh', 0):.6f}", f"{kpis.get('LCOE_eur_kwh', 0):.4f}"],
            ['Minimum DSCR', f"{kpis.get('Min_DSCR', 0):.4f}", ''],
            ['Average DSCR', f"{kpis.get('Avg_DSCR', 0):.4f}", ''],
            ['Payback Period (years)', f"{kpis.get('Payback_years', 0):.2f}", ''],
            ['', '', ''],
            ['FINANCIAL STRUCTURE', '', ''],
            ['Debt-to-Equity Ratio', f"{kpis.get('Debt_to_equity_ratio', 0):.4f}", ''],
            ['Grant Percentage', f"{kpis.get('Grant_percentage', 0):.2f}%", ''],
            ['', '', ''],
            ['PROJECT TOTALS', '', ''],
            ['Total Revenue (M EUR)', f"{kpis.get('Total_revenue', 0)/1e6:.2f}", ''],
            ['Total OPEX (M EUR)', f"{kpis.get('Total_opex', 0)/1e6:.2f}", ''],
            ['Total CAPEX (M EUR)', f"{kpis.get('Total_capex', 0)/1e6:.2f}", '']
        ]

        kpi_df = pd.DataFrame(kpi_analysis, columns=['Metric', 'Value', 'Formatted'])
        kpi_df.to_excel(writer, sheet_name='KPI_Analysis_DCF', index=False)

    def _write_npv_breakdown(self, writer, financial_results, workbook):
        """Write comprehensive NPV breakdown analysis."""
        cashflow = financial_results.get('cashflow')
        kpis = financial_results.get('kpis', {})

        if cashflow is not None:
            if isinstance(cashflow, dict):
                df = pd.DataFrame(cashflow)
            else:
                df = cashflow.copy()

            # Calculate NPV components
            discount_rate = financial_results.get('dcf_assumptions', {}).get('discount_rate', 0.08)

            # Create comprehensive NPV breakdown
            npv_breakdown = []

            # Add initial investment (Year 0)
            if 0 in df.index or len(df) > 0:
                initial_capex = df.loc[0, 'CAPEX'] if 0 in df.index and 'CAPEX' in df.columns else kpis.get('Total_capex', 0)
                if initial_capex != 0:
                    npv_breakdown.append([
                        0,
                        'Initial Investment',
                        -abs(initial_capex),
                        1.0,
                        -abs(initial_capex),
                        'CAPEX Investment'
                    ])

            # Add operating years
            for year in df.index:
                if year <= 0:
                    continue

                # Try different column names for cash flows
                equity_cf = 0
                cf_description = 'Operating Cash Flow'

                if 'Free_Cash_Flow_Equity' in df.columns:
                    equity_cf = df.loc[year, 'Free_Cash_Flow_Equity']
                elif 'Equity_CF' in df.columns:
                    equity_cf = df.loc[year, 'Equity_CF']
                elif 'Free_Cash_Flow_Project' in df.columns:
                    equity_cf = df.loc[year, 'Free_Cash_Flow_Project']
                    cf_description = 'Project Cash Flow'

                discount_factor = 1 / (1 + discount_rate) ** year
                pv_cf = equity_cf * discount_factor

                npv_breakdown.append([
                    year,
                    f'Year {year}',
                    equity_cf,
                    discount_factor,
                    pv_cf,
                    cf_description
                ])

            # Add terminal value if available
            terminal_value = kpis.get('Terminal_value', 0)
            if terminal_value != 0:
                terminal_year = len(df) if len(df) > 0 else 25
                terminal_discount_factor = 1 / (1 + discount_rate) ** terminal_year
                pv_terminal = terminal_value * terminal_discount_factor

                npv_breakdown.append([
                    terminal_year,
                    'Terminal Value',
                    terminal_value,
                    terminal_discount_factor,
                    pv_terminal,
                    'Perpetuity Value'
                ])

            npv_df = pd.DataFrame(npv_breakdown, columns=[
                'Year', 'Description', 'Cash_Flow', 'Discount_Factor', 'Present_Value', 'Type'
            ])

            # Add summary totals
            total_cf = npv_df['Cash_Flow'].sum()
            total_pv = npv_df['Present_Value'].sum()

            summary_rows = [
                ['', '', '', '', '', ''],
                ['SUMMARY', '', '', '', '', ''],
                ['Total Undiscounted CF', '', total_cf, '', '', 'Sum of all cash flows'],
                ['Net Present Value', '', '', '', total_pv, 'NPV at discount rate'],
                ['Discount Rate Used', '', f'{discount_rate:.2%}', '', '', 'WACC/Required Return'],
                ['', '', '', '', '', ''],
                ['NPV COMPONENTS', '', '', '', '', ''],
                ['Initial Investment PV', '', '', '', npv_df[npv_df['Type'] == 'CAPEX Investment']['Present_Value'].sum(), ''],
                ['Operating CF PV', '', '', '', npv_df[npv_df['Type'].str.contains('Operating|Project', na=False)]['Present_Value'].sum(), ''],
                ['Terminal Value PV', '', '', '', npv_df[npv_df['Type'] == 'Perpetuity Value']['Present_Value'].sum(), '']
            ]

            summary_df = pd.DataFrame(summary_rows, columns=npv_df.columns)
            final_df = pd.concat([npv_df, summary_df], ignore_index=True)

            final_df.to_excel(writer, sheet_name='NPV_Breakdown', index=False)

            # Format the NPV breakdown sheet
            worksheet = writer.sheets['NPV_Breakdown']

            # Set column widths
            worksheet.set_column('A:A', 8)   # Year
            worksheet.set_column('B:B', 20)  # Description
            worksheet.set_column('C:E', 15)  # Cash flow columns
            worksheet.set_column('F:F', 20)  # Type

            # Add formatting
            currency_format = workbook.add_format({'num_format': '€#,##0', 'border': 1})
            percentage_format = workbook.add_format({'num_format': '0.00%', 'border': 1})
            header_format = workbook.add_format({
                'bold': True, 'bg_color': '#2E86AB', 'font_color': 'white', 'border': 1
            })

            # Apply header formatting
            for col_num, value in enumerate(final_df.columns):
                worksheet.write(0, col_num, value, header_format)

    def _write_model_validation_2025(self, writer, financial_results, assumptions, workbook):
        """Write model validation with 2025 standards."""
        validation_notes = [
            ['ENHANCED DCF MODEL VALIDATION - 2025 EDITION', ''],
            ['', ''],
            ['MODEL FEATURES', ''],
            ['✓ Terminal Value Calculation', 'Perpetuity Growth Method'],
            ['✓ Enhanced Degradation Modeling', 'Year 1 + Annual Degradation'],
            ['✓ Working Capital Analysis', 'Dynamic WC Changes'],
            ['✓ Comprehensive OPEX Modeling', 'Base + Insurance + Land Lease'],
            ['✓ Advanced Tax Modeling', 'Holiday Periods + Escalation'],
            ['✓ Debt Schedule Optimization', 'Grace Period + Principal Repayment'],
            ['', ''],
            ['2025 MARKET STANDARDS', ''],
            ['✓ Updated WACC Benchmarks', 'Emerging Market Premiums'],
            ['✓ Current LCOE Benchmarks', 'Technology Cost Reductions'],
            ['✓ Enhanced Risk Analysis', 'Monte Carlo + Sensitivity'],
            ['✓ ESG Considerations', 'Environmental Impact Scoring'],
            ['', ''],
            ['VALIDATION CHECKS', ''],
            ['IRR Calculation Method', 'Newton-Raphson with Fallback'],
            ['NPV Discount Rate', f"{financial_results.get('dcf_assumptions', {}).get('discount_rate', 0.08):.2%}"],
            ['Terminal Growth Rate', '2.5% (Long-term Inflation)'],
            ['Cash Flow Consistency', 'Verified'],
            ['DSCR Calculation', 'EBITDA / Debt Service'],
            ['', ''],
            ['RECOMMENDATIONS', ''],
            ['• Regular model updates with actual performance data', ''],
            ['• Validate grant assumptions with official documentation', ''],
            ['• Consider currency hedging for EUR-denominated revenues', ''],
            ['• Monitor market conditions for WACC adjustments', ''],
            ['• Update technology assumptions annually', '']
        ]

        validation_df = pd.DataFrame(validation_notes, columns=['Item', 'Details'])
        validation_df.to_excel(writer, sheet_name='Model_Validation_2025', index=False)

    def _write_sensitivity_analysis_detailed(self, writer, sensitivity_df, financial_results, workbook):
        """Write detailed sensitivity analysis with interpretation."""
        # Export the raw sensitivity data
        sensitivity_df.to_excel(writer, sheet_name='Sensitivity_Analysis', index=False)

        # Create interpretation sheet
        kpis = financial_results.get('kpis', {})
        base_irr = kpis.get('IRR_project', 0)
        base_npv = kpis.get('NPV_project', 0)

        interpretation_data = [
            ['SENSITIVITY ANALYSIS INTERPRETATION', ''],
            ['', ''],
            ['BASE CASE RESULTS', ''],
            ['Base Project IRR', f'{base_irr:.2%}'],
            ['Base NPV (M EUR)', f'{base_npv/1e6:.2f}'],
            ['', ''],
            ['KEY SENSITIVITY INSIGHTS', ''],
            ['Most Sensitive Parameters:', ''],
        ]

        # Analyze sensitivity results if available
        if not sensitivity_df.empty and 'Parameter' in sensitivity_df.columns:
            # Find parameters with highest impact
            if 'IRR_Impact' in sensitivity_df.columns:
                top_sensitive = sensitivity_df.nlargest(3, 'IRR_Impact')
                for _, row in top_sensitive.iterrows():
                    interpretation_data.append([
                        f"• {row['Parameter']}",
                        f"IRR Impact: ±{row['IRR_Impact']:.1%}"
                    ])

        interpretation_data.extend([
            ['', ''],
            ['RISK ASSESSMENT', ''],
            ['High Risk Factors:', 'Parameters with >2% IRR impact'],
            ['Medium Risk Factors:', 'Parameters with 1-2% IRR impact'],
            ['Low Risk Factors:', 'Parameters with <1% IRR impact'],
            ['', ''],
            ['RECOMMENDATIONS', ''],
            ['• Focus risk mitigation on high-impact parameters', ''],
            ['• Establish monitoring systems for key variables', ''],
            ['• Consider hedging strategies for market risks', ''],
            ['• Regular model updates with actual performance', '']
        ])

        interp_df = pd.DataFrame(interpretation_data, columns=['Analysis', 'Details'])
        interp_df.to_excel(writer, sheet_name='Sensitivity_Interpretation', index=False)

    def _write_scenario_comparison(self, writer, scenarios_dict, workbook):
        """Write scenario comparison analysis."""
        scenario_data = []

        # Headers
        scenario_data.append(['SCENARIO COMPARISON ANALYSIS', '', '', ''])
        scenario_data.append(['', '', '', ''])
        scenario_data.append(['Scenario', 'Project IRR', 'NPV (M EUR)', 'Key Assumptions'])

        # Process each scenario
        for scenario_name, scenario_results in scenarios_dict.items():
            if isinstance(scenario_results, dict) and 'kpis' in scenario_results:
                kpis = scenario_results['kpis']
                irr = kpis.get('IRR_project', 0)
                npv = kpis.get('NPV_project', 0)

                scenario_data.append([
                    scenario_name.replace('_', ' ').title(),
                    f'{irr:.2%}',
                    f'{npv/1e6:.2f}',
                    scenario_results.get('description', 'Standard assumptions')
                ])

        # Add analysis
        scenario_data.extend([
            ['', '', '', ''],
            ['SCENARIO ANALYSIS', '', '', ''],
            ['Best Case Impact:', 'Upside potential assessment', '', ''],
            ['Worst Case Impact:', 'Downside risk evaluation', '', ''],
            ['Most Likely Case:', 'Expected performance range', '', ''],
            ['', '', '', ''],
            ['STRATEGIC INSIGHTS', '', '', ''],
            ['• Scenario range indicates project robustness', '', '', ''],
            ['• Focus on factors driving best/worst outcomes', '', '', ''],
            ['• Consider probability-weighted expected returns', '', '', '']
        ])

        scenario_df = pd.DataFrame(scenario_data, columns=['Item', 'Value1', 'Value2', 'Notes'])
        scenario_df.to_excel(writer, sheet_name='Scenario_Comparison', index=False)

    def _write_monte_carlo_summary(self, writer, mc_stats, workbook):
        """Write Monte Carlo analysis summary."""
        mc_data = [
            ['MONTE CARLO SIMULATION SUMMARY', ''],
            ['', ''],
            ['SIMULATION PARAMETERS', ''],
            ['Number of Simulations', mc_stats.get('n_simulations', 'N/A')],
            ['Random Seed', mc_stats.get('random_seed', 'N/A')],
            ['', ''],
            ['RESULTS DISTRIBUTION', '']
        ]

        # Add statistical results if available
        if 'IRR_project' in mc_stats:
            irr_stats = mc_stats['IRR_project']
            mc_data.extend([
                ['PROJECT IRR STATISTICS', ''],
                ['Mean', f"{irr_stats.get('mean', 0):.2%}"],
                ['Median', f"{irr_stats.get('median', 0):.2%}"],
                ['Standard Deviation', f"{irr_stats.get('std', 0):.2%}"],
                ['5th Percentile', f"{irr_stats.get('p5', 0):.2%}"],
                ['95th Percentile', f"{irr_stats.get('p95', 0):.2%}"],
                ['Probability IRR > 12%', f"{irr_stats.get('prob_above_12pct', 0):.1%}"],
                ['', '']
            ])

        if 'NPV_project' in mc_stats:
            npv_stats = mc_stats['NPV_project']
            mc_data.extend([
                ['NPV STATISTICS (M EUR)', ''],
                ['Mean', f"{npv_stats.get('mean', 0)/1e6:.2f}"],
                ['Median', f"{npv_stats.get('median', 0)/1e6:.2f}"],
                ['Standard Deviation', f"{npv_stats.get('std', 0)/1e6:.2f}"],
                ['5th Percentile', f"{npv_stats.get('p5', 0)/1e6:.2f}"],
                ['95th Percentile', f"{npv_stats.get('p95', 0)/1e6:.2f}"],
                ['Probability NPV > 0', f"{npv_stats.get('prob_positive', 0):.1%}"],
                ['', '']
            ])

        mc_data.extend([
            ['RISK ASSESSMENT', ''],
            ['Value at Risk (5%)', 'Worst case in 95% of scenarios'],
            ['Expected Shortfall', 'Average loss in worst 5% scenarios'],
            ['Confidence Level', '90% confidence interval provided'],
            ['', ''],
            ['INTERPRETATION', ''],
            ['• Results show range of possible outcomes', ''],
            ['• Consider risk tolerance vs expected returns', ''],
            ['• Use for stress testing and risk management', ''],
            ['• Regular updates with actual performance data', '']
        ])

        mc_df = pd.DataFrame(mc_data, columns=['Metric', 'Value'])
        mc_df.to_excel(writer, sheet_name='Monte_Carlo_Summary', index=False)

    def _write_risk_analysis(self, writer, financial_results, assumptions, workbook):
        """Write comprehensive risk analysis and recommendations."""
        kpis = financial_results.get('kpis', {})

        risk_data = [
            ['COMPREHENSIVE RISK ANALYSIS - 2025', ''],
            ['', ''],
            ['FINANCIAL RISK ASSESSMENT', ''],
            ['Project IRR vs Threshold (12%)', self._assess_risk_level(kpis.get('IRR_project', 0), 0.12, 'above')],
            ['DSCR vs Minimum (1.25)', self._assess_risk_level(kpis.get('Min_DSCR', 0), 1.25, 'above')],
            ['LCOE Competitiveness', self._assess_lcoe_risk(kpis.get('LCOE_eur_kwh', 0))],
            ['Payback Period', self._assess_payback_risk(kpis.get('Payback_years', 0))],
            ['', ''],
            ['MARKET RISKS', ''],
            ['Electricity Price Risk', 'Medium - Long-term PPA recommended'],
            ['Currency Risk (EUR/MAD)', 'Medium - Consider hedging strategies'],
            ['Regulatory Risk', 'Low - Stable renewable energy policy'],
            ['Technology Risk', 'Low - Proven solar PV technology'],
            ['', ''],
            ['OPERATIONAL RISKS', ''],
            ['Performance Risk', 'Low - Conservative degradation assumptions'],
            ['Maintenance Risk', 'Medium - Ensure quality O&M contracts'],
            ['Grid Connection Risk', 'Medium - Verify grid capacity'],
            ['Force Majeure Risk', 'Low - Insurance coverage recommended'],
            ['', ''],
            ['FINANCING RISKS', ''],
            ['Interest Rate Risk', 'Medium - Fixed rate debt preferred'],
            ['Refinancing Risk', 'Low - Conservative debt structure'],
            ['Grant Risk', 'Medium - Verify grant documentation'],
            ['Equity Risk', 'Low - Strong sponsor commitment'],
            ['', ''],
            ['MITIGATION STRATEGIES', ''],
            ['• Secure long-term PPA with creditworthy offtaker', ''],
            ['• Implement comprehensive insurance program', ''],
            ['• Establish performance monitoring systems', ''],
            ['• Maintain adequate debt service reserves', ''],
            ['• Regular financial model updates', ''],
            ['• Diversify supplier and contractor base', ''],
            ['', ''],
            ['RECOMMENDATIONS BY PRIORITY', ''],
            ['HIGH PRIORITY', ''],
            ['1. Finalize PPA negotiations', ''],
            ['2. Complete technical due diligence', ''],
            ['3. Secure construction financing', ''],
            ['MEDIUM PRIORITY', ''],
            ['4. Implement currency hedging', ''],
            ['5. Finalize insurance arrangements', ''],
            ['6. Establish monitoring systems', ''],
            ['LOW PRIORITY', ''],
            ['7. Consider refinancing options', ''],
            ['8. Plan for technology upgrades', '']
        ]

        risk_df = pd.DataFrame(risk_data, columns=['Risk Factor', 'Assessment'])
        risk_df.to_excel(writer, sheet_name='Risk_Analysis', index=False)

    def _assess_risk_level(self, actual_value, threshold, direction):
        """Assess risk level based on threshold comparison."""
        if direction == 'above':
            if actual_value >= threshold * 1.2:
                return 'LOW RISK - Well above threshold'
            elif actual_value >= threshold:
                return 'MEDIUM RISK - Above threshold'
            else:
                return 'HIGH RISK - Below threshold'
        else:
            if actual_value <= threshold * 0.8:
                return 'LOW RISK - Well below threshold'
            elif actual_value <= threshold:
                return 'MEDIUM RISK - Below threshold'
            else:
                return 'HIGH RISK - Above threshold'

    def _assess_lcoe_risk(self, lcoe):
        """Assess LCOE competitiveness risk."""
        if lcoe <= 0.035:
            return 'LOW RISK - Highly competitive'
        elif lcoe <= 0.045:
            return 'MEDIUM RISK - Competitive'
        else:
            return 'HIGH RISK - Above market rates'

    def _assess_payback_risk(self, payback_years):
        """Assess payback period risk."""
        if payback_years <= 8:
            return 'LOW RISK - Quick payback'
        elif payback_years <= 12:
            return 'MEDIUM RISK - Reasonable payback'
        else:
            return 'HIGH RISK - Long payback period'

    def _write_charts_reference(self, writer, workbook):
        """Write charts reference sheet with information about generated charts."""
        charts_info = [
            ['FINANCIAL ANALYSIS CHARTS REFERENCE', ''],
            ['', ''],
            ['CHART LOCATIONS', ''],
            ['Charts Directory', 'charts/ folder in output directory'],
            ['Chart Format', 'PNG (300 DPI for high quality)'],
            ['Chart Naming', 'Descriptive names with underscores'],
            ['', ''],
            ['AVAILABLE CHARTS', ''],
            ['Financial KPIs Bar Chart', 'financial_kpis.png'],
            ['• Shows key performance indicators', ''],
            ['• Project IRR, Equity IRR, LCOE, Min DSCR', ''],
            ['', ''],
            ['Cash Flow Analysis Line Chart', 'cash_flow_analysis.png'],
            ['• Project and equity cash flows over time', ''],
            ['• Trend analysis and cash flow patterns', ''],
            ['', ''],
            ['DCF Waterfall Chart', 'dcf_waterfall.png'],
            ['• Value creation breakdown', ''],
            ['• Initial investment to NPV components', ''],
            ['', ''],
            ['HYPOTHESIS TESTING CHARTS', ''],
            ['LCOE Incentive Waterfall', 'lcoe_incentive_waterfall.png'],
            ['• H2: LCOE impact analysis with incentive breakdown', ''],
            ['• Shows cost reduction from each grant type', ''],
            ['', ''],
            ['IRR Scenario Comparison', 'irr_scenario_comparison.png'],
            ['• H1: IRR improvement analysis for 5pp target', ''],
            ['• Compares base case vs incentivized scenarios', ''],
            ['', ''],
            ['Financing Structure Comparison', 'financing_structure_comparison.png'],
            ['• H2: Cross-financing strategy analysis', ''],
            ['• Piano Mattei + Morocco combination benefits', ''],
            ['', ''],
            ['Location Impact Comparison', 'location_impact_comparison.png'],
            ['• H3: Location advantage analysis', ''],
            ['• Dakhla vs Ouarzazate performance comparison', ''],
            ['', ''],
            ['CHART USAGE INSTRUCTIONS', ''],
            ['Excel Integration:', 'Charts saved as separate files'],
            ['DOCX Integration:', 'Charts embedded in document'],
            ['HTML Integration:', 'Charts embedded as base64 images'],
            ['PowerPoint Use:', 'Import PNG files from charts directory'],
            ['', ''],
            ['CHART CUSTOMIZATION', ''],
            ['Resolution:', '300 DPI for print quality'],
            ['Format:', 'PNG with transparent background'],
            ['Size:', 'Optimized for A4 documents'],
            ['Colors:', 'Professional color scheme'],
            ['', ''],
            ['TECHNICAL NOTES', ''],
            ['• Charts generated using matplotlib', ''],
            ['• High-resolution export for presentations', ''],
            ['• Consistent styling across all charts', ''],
            ['• Financial data formatted with currency symbols', ''],
            ['• Automatic scaling for different data ranges', '']
        ]

        charts_df = pd.DataFrame(charts_info, columns=['Information', 'Details'])
        charts_df.to_excel(writer, sheet_name='Charts_Reference', index=False)
